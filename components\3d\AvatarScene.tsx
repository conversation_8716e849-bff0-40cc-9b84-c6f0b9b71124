import React, { useRef, useEffect, useState } from "react";
import { StyleSheet, View, Text, Platform } from "react-native";
import { Canvas } from "@react-three/fiber/native";
import { OrbitControls } from "@react-three/drei/native";
import { Avatar } from "./Avatar";
import { useAvatarStore } from "@/stores/avatarStore";
import Colors from "@/constants/colors";

export function AvatarScene() {
  const { avatarAction } = useAvatarStore();
  const controlsRef = useRef<any>(null);
  const [isWeb] = useState(Platform.OS === "web");
  const [error, setError] = useState<string | null>(null);

  // Reset camera position when action changes
  useEffect(() => {
    if (controlsRef.current && controlsRef.current.reset) {
      controlsRef.current.reset();
    }
  }, [avatarAction]);

  // Error handler for the Canvas
  const handleCanvasError = () => {
    console.error("Canvas error occurred");
    setError("Failed to load 3D scene");
  };

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Text style={styles.errorSubtext}>Please try again later</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isWeb ? (
        // Web fallback - simpler scene without advanced features
        <Canvas
          style={styles.canvas}
          camera={{ position: [0, 1.5, 3], fov: 50 }}
          shadows
        >
          <ambientLight intensity={0.8} />
          <directionalLight
            position={[5, 5, 5]}
            intensity={1}
            castShadow
          />
          <Avatar />
          <OrbitControls
            ref={controlsRef}
            enableZoom={false}
            enablePan={false}
            minPolarAngle={Math.PI / 3}
            maxPolarAngle={Math.PI / 1.8}
          />
        </Canvas>
      ) : (
        // Native implementation with full features
        <Canvas
          style={styles.canvas}
          camera={{ position: [0, 1.5, 3], fov: 50 }}
          shadows
        >
          <ambientLight intensity={0.8} />
          <directionalLight
            position={[5, 5, 5]}
            intensity={1}
            castShadow
          />
          <Avatar />
          <OrbitControls
            ref={controlsRef}
            enableZoom={false}
            enablePan={false}
            minPolarAngle={Math.PI / 3}
            maxPolarAngle={Math.PI / 1.8}
          />
        </Canvas>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
  },
  canvas: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    textAlign: "center",
  },
  errorSubtext: {
    fontSize: 14,
    color: Colors.textLight,
    marginTop: 8,
    textAlign: "center",
  },
});