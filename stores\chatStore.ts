import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Message } from "@/types/chat";

interface ChatState {
  messages: Message[];
  addMessage: (message: Message) => void;
  clearMessages: () => void;
}

export const useChatStore = create<ChatState>()(
  persist(
    (set) => ({
      messages: [
        {
          id: "1",
          text: "Hello! I'm your virtual companion. How can I help you today?",
          sender: "ai",
          timestamp: new Date(),
        },
      ],
      addMessage: (message) => 
        set((state) => ({ 
          messages: [...state.messages, message] 
        })),
      clearMessages: () => 
        set({ 
          messages: [
            {
              id: "1",
              text: "Hello! I'm your virtual companion. How can I help you today?",
              sender: "ai",
              timestamp: new Date(),
            },
          ] 
        }),
    }),
    {
      name: "chat-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);