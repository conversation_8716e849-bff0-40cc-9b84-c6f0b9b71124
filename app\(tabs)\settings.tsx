import React, { useState } from "react";
import { StyleSheet, View, Text, TouchableOpacity, Switch, ScrollView, Platform, Alert } from "react-native";
import { StatusBar } from "expo-status-bar";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import * as Haptics from "expo-haptics";
import { ChevronRight, User, Moon, Volume2, Bell, HelpCircle } from "lucide-react-native";

import Colors from "@/constants/colors";
import { useAvatarStore } from "@/stores/avatarStore";

export default function SettingsScreen() {
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [sound, setSound] = useState(true);
  const { avatarUrl, setAvatarUrl } = useAvatarStore();

  const handleToggle = (setting: string, value: boolean) => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    switch (setting) {
      case "darkMode":
        setDarkMode(value);
        break;
      case "notifications":
        setNotifications(value);
        break;
      case "sound":
        setSound(value);
        break;
    }
  };

  const handleChangeAvatar = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    // Toggle between two avatar URLs for demo purposes
    const newAvatarUrl = avatarUrl === "https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb" 
      ? "https://models.readyplayer.me/645a99c9c6c3b9e9f7c95c6b.glb"
      : "https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb";
    
    Alert.alert(
      "Change Avatar",
      "This feature is currently in development. The default avatar will be used for now.",
      [{ text: "OK", onPress: () => console.log("OK Pressed") }]
    );
    
    // For now, we'll just use the default avatar to ensure it works
    setAvatarUrl("https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb");
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={[Colors.gradientStart, Colors.gradientEnd]}
        style={styles.background}
      />
      
      <SafeAreaView style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
        </View>
        
        <ScrollView 
          style={styles.settingsContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Appearance</Text>
            
            <TouchableOpacity style={styles.settingItem} onPress={handleChangeAvatar}>
              <View style={styles.settingLeft}>
                <User size={22} color={Colors.primary} />
                <Text style={styles.settingText}>Change Avatar</Text>
              </View>
              <ChevronRight size={20} color={Colors.textLight} />
            </TouchableOpacity>
            
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <Moon size={22} color={Colors.primary} />
                <Text style={styles.settingText}>Dark Mode</Text>
              </View>
              <Switch
                value={darkMode}
                onValueChange={(value) => handleToggle("darkMode", value)}
                trackColor={{ false: Colors.switchTrack, true: Colors.primary }}
                thumbColor={Colors.white}
              />
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Preferences</Text>
            
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <Bell size={22} color={Colors.primary} />
                <Text style={styles.settingText}>Notifications</Text>
              </View>
              <Switch
                value={notifications}
                onValueChange={(value) => handleToggle("notifications", value)}
                trackColor={{ false: Colors.switchTrack, true: Colors.primary }}
                thumbColor={Colors.white}
              />
            </View>
            
            <View style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <Volume2 size={22} color={Colors.primary} />
                <Text style={styles.settingText}>Sound Effects</Text>
              </View>
              <Switch
                value={sound}
                onValueChange={(value) => handleToggle("sound", value)}
                trackColor={{ false: Colors.switchTrack, true: Colors.primary }}
                thumbColor={Colors.white}
              />
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingLeft}>
                <HelpCircle size={22} color={Colors.primary} />
                <Text style={styles.settingText}>Help & FAQ</Text>
              </View>
              <ChevronRight size={20} color={Colors.textLight} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.footer}>
            <Text style={styles.version}>Version 1.0.0</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingTop: 10,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  settingsContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 15,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 15,
  },
  footer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  version: {
    fontSize: 14,
    color: Colors.textLight,
  },
});