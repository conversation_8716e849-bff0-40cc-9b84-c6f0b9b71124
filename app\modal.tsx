import React from "react";
import { StatusBar } from "expo-status-bar";
import { Platform, StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { X } from "lucide-react-native";
import Colors from "@/constants/colors";

export default function ModalScreen() {
  const router = useRouter();
  
  return (
    <View style={styles.container}>
      <StatusBar style={Platform.OS === "ios" ? "light" : "auto"} />
      
      <TouchableOpacity 
        style={styles.closeButton}
        onPress={() => router.back()}
      >
        <X size={24} color={Colors.text} />
      </TouchableOpacity>
      
      <Text style={styles.title}>About Virtual Companion</Text>
      
      <View style={styles.content}>
        <Text style={styles.paragraph}>
          This app demonstrates the integration of 3D avatars in a React Native application using React Three Fiber and Expo.
        </Text>
        
        <Text style={styles.paragraph}>
          The avatar is powered by Ready Player Me, allowing for customizable 3D characters that can respond to user interactions.
        </Text>
        
        <Text style={styles.paragraph}>
          Features include:
        </Text>
        
        <View style={styles.featureList}>
          <Text style={styles.feature}>• 3D character visualization</Text>
          <Text style={styles.feature}>• Interactive chat interface</Text>
          <Text style={styles.feature}>• Character animations</Text>
          <Text style={styles.feature}>• Customizable settings</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    padding: 20,
  },
  closeButton: {
    position: "absolute",
    top: Platform.OS === "ios" ? 50 : 20,
    right: 20,
    zIndex: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    textAlign: "center",
    marginTop: Platform.OS === "ios" ? 60 : 30,
    marginBottom: 30,
  },
  content: {
    flex: 1,
    paddingHorizontal: 10,
  },
  paragraph: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 20,
  },
  featureList: {
    marginTop: 10,
    marginBottom: 30,
  },
  feature: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 28,
  },
});