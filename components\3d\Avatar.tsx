import React, { useRef, useEffect, useState } from "react";
import { Platform, Text, View, StyleSheet } from "react-native";
import { useFrame } from "@react-three/fiber/native";
import { useGLTF } from "@react-three/drei/native";
import * as THREE from "three";
import { useAvatarStore } from "@/stores/avatarStore";

type AnimationAction = THREE.AnimationAction;
type AnimationMixer = THREE.AnimationMixer;

interface AnimationActions {
  idle?: AnimationAction;
  wave?: AnimationAction;
  talking?: AnimationAction;
  thinking?: AnimationAction;
  [key: string]: AnimationAction | undefined;
}

export function Avatar() {
  const { avatarUrl, avatarAction } = useAvatarStore();
  const group = useRef<THREE.Group>(null);
  const mixer = useRef<AnimationMixer | null>(null);
  const actions = useRef<AnimationActions>({});
  const previousAction = useRef<AnimationAction | null>(null);
  const currentAction = useRef<AnimationAction | null>(null);
  const [modelError, setModelError] = useState<string | null>(null);
  
  // Use a fallback URL if the main one fails
  const modelUrl = "https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb";
  
  // Load the GLTF model
  const { scene, animations } = useGLTF(modelUrl);
  
  // Handle potential errors
  useEffect(() => {
    const handleError = () => {
      console.error("Failed to load model");
      setModelError("Failed to load 3D model");
    };

    if (!scene) {
      handleError();
    }
  }, [scene]);
  
  // Set up animations
  useEffect(() => {
    if (!scene || animations.length === 0) return;
    
    // Create animation mixer
    mixer.current = new THREE.AnimationMixer(scene);
    
    // Create animation actions
    actions.current = {
      idle: mixer.current.clipAction(
        animations.find((clip) => clip.name === "Idle") || animations[0]
      ),
      wave: mixer.current.clipAction(
        animations.find((clip) => clip.name === "Wave") || animations[0]
      ),
      talking: mixer.current.clipAction(
        animations.find((clip) => clip.name === "Talking") || animations[0]
      ),
      thinking: mixer.current.clipAction(
        animations.find((clip) => clip.name === "Thinking") || animations[0]
      ),
    };
    
    // Set default action
    previousAction.current = actions.current.idle || null;
    currentAction.current = actions.current.idle || null;
    if (currentAction.current) {
      currentAction.current.play();
    }
    
    return () => {
      // Clean up animations
      if (mixer.current) {
        mixer.current.stopAllAction();
      }
    };
  }, [scene, animations]);
  
  // Handle action changes
  useEffect(() => {
    if (!mixer.current || !actions.current) return;
    
    const actionToPlay = actions.current[avatarAction] || actions.current.idle;
    if (!actionToPlay) return;
    
    // Fade from previous to current action
    previousAction.current = currentAction.current;
    currentAction.current = actionToPlay;
    
    if (previousAction.current !== currentAction.current && previousAction.current && currentAction.current) {
      previousAction.current.fadeOut(0.5);
      currentAction.current.reset().fadeIn(0.5).play();
      
      // If action is not idle, return to idle after a delay
      if (avatarAction !== "idle") {
        const timer = setTimeout(() => {
          if (actions.current.idle) {
            previousAction.current = currentAction.current;
            currentAction.current = actions.current.idle;
            if (previousAction.current && currentAction.current) {
              previousAction.current.fadeOut(0.5);
              currentAction.current.reset().fadeIn(0.5).play();
            }
          }
        }, 3000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [avatarAction]);
  
  // Update animation mixer on each frame
  useFrame((_, delta) => {
    if (mixer.current) {
      mixer.current.update(delta);
    }
  });
  
  // If there's an error loading the model, return a placeholder
  if (modelError) {
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="hotpink" />
      </mesh>
    );
  }
  
  // Handle platform-specific rendering
  if (Platform.OS === "web") {
    // Simplified model for web
    return (
      <group ref={group} dispose={null}>
        <primitive object={scene} scale={1.5} position={[0, -1.5, 0]} />
      </group>
    );
  }
  
  // Full model for native
  return (
    <group ref={group} dispose={null}>
      <primitive object={scene} scale={1.5} position={[0, -1.5, 0]} />
    </group>
  );
}

// Preload the model
useGLTF.preload("https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb");