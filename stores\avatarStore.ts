import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

type AvatarAction = "idle" | "wave" | "talking" | "thinking";

interface AvatarState {
  avatarUrl: string;
  avatarAction: AvatarAction;
  setAvatarUrl: (url: string) => void;
  setAvatarAction: (action: AvatarAction) => void;
}

export const useAvatarStore = create<AvatarState>()(
  persist(
    (set) => ({
      // Updated to a valid Ready Player Me avatar URL
      avatarUrl: "https://models.readyplayer.me/6825ddb33c5ab94b9e884b09.glb",
      avatarAction: "idle",
      setAvatarUrl: (url) => set({ avatarUrl: url }),
      setAvatarAction: (action) => set({ avatarAction: action }),
    }),
    {
      name: "avatar-storage",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);